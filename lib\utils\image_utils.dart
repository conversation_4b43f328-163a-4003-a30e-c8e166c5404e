import 'package:flutter/material.dart';

/// Utility functions for handling image URLs and processing
class ImageUtils {
  /// Cleans malformed image URLs that have double storage paths
  ///
  /// This fixes URLs like:
  /// https://mrgarment.braincavesoft.com/storage/https://mrgarment.braincavesoft.com/storage/catalogs/...
  ///
  /// And converts them to:
  /// https://mrgarment.braincavesoft.com/storage/catalogs/...
  static String cleanImageUrl(String url) {
    if (url.startsWith(
      'https://mrgarment.braincavesoft.com/storage/https://',
    )) {
      return url.replaceFirst(
        'https://mrgarment.braincavesoft.com/storage/',
        '',
      );
    }
    return url;
  }

  /// Cleans a list of image URLs
  static List<String> cleanImageUrls(List<String> urls) {
    return urls.map((url) => cleanImageUrl(url)).toList();
  }

  /// Builds a network image widget with proper error handling and loading states
  static Widget buildNetworkImage(
    String imageUrl, {
    BoxFit fit = BoxFit.cover,
    double? width,
    double? height,
  }) {
    return Image.network(
      cleanImageUrl(imageUrl),
      fit: fit,
      width: width,
      height: height,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return Container(
          color: Colors.grey[200],
          child: const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF005368)),
            ),
          ),
        );
      },
      errorBuilder: (context, error, stackTrace) {
        return Container(
          color: Colors.grey[200],
          child: const Center(
            child: Icon(Icons.broken_image, color: Colors.grey, size: 24),
          ),
        );
      },
    );
  }
}
