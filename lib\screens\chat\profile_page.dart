import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
// import 'package:mr_garments_mobile/services/chat_service.dart';
import 'package:mr_garments_mobile/models/chat_user.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  ChatUser? _currentUser;
  bool _isLoading = true;
  // Map<String, dynamic> _stats = {};

  @override
  void initState() {
    super.initState();
    _loadProfile();
  }

  Future<void> _loadProfile() async {
    try {
      setState(() => _isLoading = true);

      final userId = await SessionService.getUserId();
      final userName = await SessionService.getUserName();
      final userEmail = await SessionService.getUserEmail();
      final userRole = await SessionService.getUserRole();

      if (userId != null &&
          userName != null &&
          userEmail != null &&
          userRole != null) {
        _currentUser = ChatUser(
          id: userId.toString(),
          name: userName,
          email: userEmail,
          role: userRole,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Load chat statistics
        // await _loadStats();
      }

      setState(() => _isLoading = false);
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        AppSnackbar.showError(context, 'Error loading profile: $e');
      }
    }
  }

  // Future<void> _loadStats() async {
  //   try {
  //     if (_currentUser == null) return;

  //     // Get user's chats (convert stream to list)
  //     final chatsStream = ChatService.getUserChatsStream(_currentUser!.id);
  //     final chats = await chatsStream.first;

  //     // Get user's groups
  //     final groups = await ChatService.getUserGroups(_currentUser!.id);

  //     // Calculate unread messages
  //     final unreadCount = await ChatService.getUnreadMessageCount(
  //       _currentUser!.id,
  //     );

  //     setState(() {
  //       _stats = {
  //         'totalChats': chats.length,
  //         'totalGroups': groups.length,
  //         'unreadMessages': unreadCount,
  //       };
  //     });
  //   } catch (e) {
  //     print('Error loading stats: $e');
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        backgroundColor: Colors.white,
        body: Center(
          child: CircularProgressIndicator(color: Color(0xFF005368)),
        ),
      );
    }

    if (_currentUser == null) {
      return Scaffold(
        backgroundColor: Colors.white,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                LucideIcons.userX,
                size: 80,
                color: Colors.grey.withOpacity(0.5),
              ),
              const SizedBox(height: 16),
              Text(
                'Profile Not Available',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Please log in to view your profile',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.white,
      body: RefreshIndicator(
        onRefresh: _loadProfile,
        color: const Color(0xFF005368),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              const SizedBox(height: 20),

              // Profile Header
              _buildProfileHeader(),

              const SizedBox(height: 30),

              // Stats Section
              // _buildStatsSection(),

              const SizedBox(height: 30),

              // Profile Details
              _buildProfileDetails(),

              const SizedBox(height: 30),

              // Chat Settings
              // _buildChatSettings(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF005368), Color(0xFF007A8C)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF005368).withOpacity(0.3),
            spreadRadius: 2,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Profile Image
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 4),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  spreadRadius: 2,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: CircleAvatar(
              radius: 46,
              backgroundColor: Colors.white,
              backgroundImage:
                  _currentUser!.profileImageUrl != null
                      ? NetworkImage(_currentUser!.profileImageUrl!)
                      : null,
              child:
                  _currentUser!.profileImageUrl == null
                      ? Text(
                        _currentUser!.name.isNotEmpty
                            ? _currentUser!.name[0].toUpperCase()
                            : 'U',
                        style: GoogleFonts.poppins(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF005368),
                        ),
                      )
                      : null,
            ),
          ),

          const SizedBox(height: 16),

          // Name
          Text(
            _currentUser!.name,
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),

          const SizedBox(height: 4),

          // Role Badge
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.white.withOpacity(0.3)),
            ),
            child: Text(
              _currentUser!.role.toUpperCase(),
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.white,
                letterSpacing: 1,
              ),
            ),
          ),

          const SizedBox(height: 8),

          // Email
          Text(
            _currentUser!.email,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.white.withOpacity(0.9),
            ),
          ),
        ],
      ),
    );
  }

  // Widget _buildStatsSection() {
  //   return Container(
  //     padding: const EdgeInsets.all(20),
  //     decoration: BoxDecoration(
  //       color: Colors.white,
  //       borderRadius: BorderRadius.circular(16),
  //       boxShadow: [
  //         BoxShadow(
  //           color: Colors.grey.withOpacity(0.1),
  //           spreadRadius: 1,
  //           blurRadius: 10,
  //           offset: const Offset(0, 2),
  //         ),
  //       ],
  //     ),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Text(
  //           'Chat Statistics',
  //           style: GoogleFonts.poppins(
  //             fontSize: 18,
  //             fontWeight: FontWeight.w600,
  //             color: const Color(0xFF005368),
  //           ),
  //         ),
  //         const SizedBox(height: 16),

  //         Row(
  //           children: [
  //             Expanded(
  //               child: _buildStatItem(
  //                 icon: LucideIcons.messageCircle,
  //                 label: 'Total Chats',
  //                 value: _stats['totalChats']?.toString() ?? '0',
  //                 color: Colors.blue,
  //               ),
  //             ),
  //             Expanded(
  //               child: _buildStatItem(
  //                 icon: LucideIcons.users,
  //                 label: 'Groups',
  //                 value: _stats['totalGroups']?.toString() ?? '0',
  //                 color: Colors.green,
  //               ),
  //             ),
  //             Expanded(
  //               child: _buildStatItem(
  //                 icon: LucideIcons.bell,
  //                 label: 'Unread',
  //                 value: _stats['unreadMessages']?.toString() ?? '0',
  //                 color: Colors.orange,
  //               ),
  //             ),
  //           ],
  //         ),
  //       ],
  //     ),
  //   );
  // }

  // Widget _buildStatItem({
  //   required IconData icon,
  //   required String label,
  //   required String value,
  //   required Color color,
  // }) {
  //   return Column(
  //     children: [
  //       Container(
  //         width: 50,
  //         height: 50,
  //         decoration: BoxDecoration(
  //           color: color.withOpacity(0.1),
  //           shape: BoxShape.circle,
  //         ),
  //         child: Icon(icon, color: color, size: 24),
  //       ),
  //       const SizedBox(height: 8),
  //       Text(
  //         value,
  //         style: GoogleFonts.poppins(
  //           fontSize: 20,
  //           fontWeight: FontWeight.bold,
  //           color: color,
  //         ),
  //       ),
  //       Text(
  //         label,
  //         style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[600]),
  //         textAlign: TextAlign.center,
  //       ),
  //     ],
  //   );
  // }

  Widget _buildProfileDetails() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Profile Information',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF005368),
            ),
          ),
          const SizedBox(height: 16),

          _buildDetailItem(
            icon: LucideIcons.user,
            label: 'Full Name',
            value: _currentUser!.name,
          ),

          _buildDetailItem(
            icon: LucideIcons.mail,
            label: 'Email Address',
            value: _currentUser!.email,
          ),

          _buildDetailItem(
            icon: LucideIcons.shield,
            label: 'Account Type',
            value: _currentUser!.role.toUpperCase(),
          ),

          // _buildDetailItem(
          //   icon: LucideIcons.calendar,
          //   label: 'Member Since',
          //   value: _formatDate(_currentUser!.createdAt),
          // ),
        ],
      ),
    );
  }

  Widget _buildDetailItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: const Color(0xFF005368).withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(icon, color: const Color(0xFF005368), size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  value,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Widget _buildChatSettings() {
  //   return Container(
  //     padding: const EdgeInsets.all(20),
  //     decoration: BoxDecoration(
  //       color: Colors.white,
  //       borderRadius: BorderRadius.circular(16),
  //       boxShadow: [
  //         BoxShadow(
  //           color: Colors.grey.withOpacity(0.1),
  //           spreadRadius: 1,
  //           blurRadius: 10,
  //           offset: const Offset(0, 2),
  //         ),
  //       ],
  //     ),
      // child: Column(
      //   crossAxisAlignment: CrossAxisAlignment.start,
      //   children: [
          // Text(
          //   'Chat Settings',
          //   style: GoogleFonts.poppins(
          //     fontSize: 18,
          //     fontWeight: FontWeight.w600,
          //     color: const Color(0xFF005368),
          //   ),
          // ),
          // const SizedBox(height: 16),

          // _buildSettingItem(
          //   icon: LucideIcons.bell,
          //   title: 'Notifications',
          //   subtitle: 'Manage chat notifications',
          //   onTap: () {
          //     AppSnackbar.showInfo(
          //       context,
          //       'Notification settings coming soon',
          //     );
          //   },
          // ),

          // _buildSettingItem(
          //   icon: LucideIcons.lock,
          //   title: 'Privacy',
          //   subtitle: 'Control who can message you',
          //   onTap: () {
          //     AppSnackbar.showInfo(context, 'Privacy settings coming soon');
          //   },
          // ),

          // _buildSettingItem(
          //   icon: LucideIcons.download,
          //   title: 'Export Chat Data',
          //   subtitle: 'Download your chat history',
          //   onTap: () {
          //     AppSnackbar.showInfo(context, 'Export feature coming soon');
          //   },
          // ),
  //       ],
  //     ),
    // );
  }

  // Widget _buildSettingItem({
  //   required IconData icon,
  //   required String title,
  //   required String subtitle,
  //   required VoidCallback onTap,
  // }) {
  //   return ListTile(
  //     onTap: onTap,
  //     contentPadding: EdgeInsets.zero,
  //     leading: Container(
  //       width: 40,
  //       height: 40,
  //       decoration: BoxDecoration(
  //         color: const Color(0xFF005368).withOpacity(0.1),
  //         borderRadius: BorderRadius.circular(10),
  //       ),
  //       child: Icon(icon, color: const Color(0xFF005368), size: 20),
  //     ),
  //     title: Text(
  //       title,
  //       style: GoogleFonts.poppins(
  //         fontSize: 14,
  //         fontWeight: FontWeight.w500,
  //         color: Colors.black87,
  //       ),
  //     ),
  //     subtitle: Text(
  //       subtitle,
  //       style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[600]),
  //     ),
  //     trailing: Icon(
  //       LucideIcons.chevronRight,
  //       color: Colors.grey[400],
  //       size: 20,
  //     ),
  //   );
  // }

  // String _formatDate(DateTime date) {
  //   final months = [
  //     'Jan',
  //     'Feb',
  //     'Mar',
  //     'Apr',
  //     'May',
  //     'Jun',
  //     'Jul',
  //     'Aug',
  //     'Sep',
  //     'Oct',
  //     'Nov',
  //     'Dec',
  //   ];
  //   return '${months[date.month - 1]} ${date.day}, ${date.year}';
  // }
// }
