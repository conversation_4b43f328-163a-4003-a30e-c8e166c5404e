import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';

import 'package:mr_garments_mobile/screens/admin/admin_home_page.dart';
import 'package:mr_garments_mobile/screens/users/add_edit_user.dart';
import 'package:mr_garments_mobile/screens/users/new_user_request_tab.dart';
import 'package:mr_garments_mobile/screens/users/staff_request_tab.dart';
import 'package:mr_garments_mobile/screens/users/retailer_request_tab.dart';
import 'package:mr_garments_mobile/screens/users/users_tab.dart';

class UsersScreen extends ConsumerStatefulWidget {
  const UsersScreen({super.key});

  @override
  ConsumerState<UsersScreen> createState() => _UsersScreenState();
}

class _UsersScreenState extends ConsumerState<UsersScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  Timer? _debounce;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _searchController.addListener(() {
      if (_debounce?.isActive ?? false) _debounce!.cancel();
      _debounce = Timer(const Duration(milliseconds: 300), () {
        setState(() {}); // refresh after user stops typing for 300ms
      });
    });
  }

  Widget _buildAppBar() {
    return AppBar(
      backgroundColor: const Color(0xFF005368),
      elevation: 0,
      automaticallyImplyLeading: false,
      title: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: const Icon(Icons.arrow_back, color: Colors.white),
          ),
          const SizedBox(width: 16),
          Text(
            "Users",
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap:
                () => Navigator.pushAndRemoveUntil(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AdminHomePage(),
                  ),
                  (route) => false,
                ),
            child: const Icon(Icons.home, color: Colors.white),
          ),
        ],
      ),
      bottom: TabBar(
        controller: _tabController,
        indicatorColor: Colors.white,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        tabs: [
          Tab(child: Text("Users", style: GoogleFonts.poppins())),
          Tab(child: Text("User Requests", style: GoogleFonts.poppins())),
          Tab(child: Text("Staff Requests", style: GoogleFonts.poppins())),
          Tab(child: Text("Retailer Requests", style: GoogleFonts.poppins())),
        ],
      ),
    );
  }

  Widget _buildSearchAndAddUser() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: "Search users...",
                prefixIcon: const Icon(Icons.search),
                filled: true,
                fillColor: Color.fromARGB(248, 240, 240, 240),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(30),
                  borderSide: BorderSide.none,
                ),
              ),
            ),
          ),
          const SizedBox(width: 10),
          IconButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(0xFFF2A738),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 11, vertical: 10),
            ),
            onPressed: () {
              // Navigate to Add User screen
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => AddEditUser()),
              );
            },
            icon: const Icon(Icons.add),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Widget _buildUserTabContent() {
    return Column(
      children: [
        _buildSearchAndAddUser(),
        Expanded(child: UsersTab(searchQuery: _searchController.text)),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(90),
        child: _buildAppBar(),
      ),
      body: Column(
        children: [
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildUserTabContent(),
                const NewUserRequestTab(),
                const StaffRequestTab(),
                const RetailerRequestTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
