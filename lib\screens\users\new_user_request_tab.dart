import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/providers/user_provider.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

class NewUserRequestTab extends ConsumerWidget {
  const NewUserRequestTab({super.key});

  void _handleAction(
    BuildContext context,
    WidgetRef ref,
    int userId,
    String action,
  ) async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => const Center(child: CircularProgressIndicator()),
    );

    try {
      await ref.read(usersProvider.notifier).verifyUser(userId, action);
      if (!context.mounted) return;
      Navigator.pop(context);
      AppSnackbar.showSuccess(
        context,
        "User ${action == 'approve' ? 'approved' : 'rejected'} successfully",
      );

      // Navigate to Users screen, so admin sees updated list:
      // Navigator.pop(context);  // Close current screen
    } catch (e) {
      Navigator.pop(context);
      if (context.mounted) {
        AppSnackbar.showError(context, 'Error: ${e.toString()}');
      }
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final requestsAsync = ref.watch(usersProvider).newRequests;

    return requestsAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (e, _) => Center(child: Text("Error: $e")),
      data: (requests) {
        // Filter out staff requests - they should only appear in Staff Requests tab
        final userRequests =
            requests
                .where(
                  (request) =>
                      request['account_type'] != 'staff' &&
                      request['role'] != 'Staff' &&
                      request['role'] != 'staff',
                )
                .toList();

        if (userRequests.isEmpty) {
          return const Center(child: Text("No new user requests found"));
        }
        return ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          itemCount: userRequests.length,
          itemBuilder: (context, index) {
            final user = userRequests[index];
            return Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              elevation: 3,
              margin: const EdgeInsets.only(bottom: 16),
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 14,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user['name'] ?? '',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      user['email'] ?? '',
                      style: GoogleFonts.poppins(
                        fontSize: 13,
                        color: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      "Requested Role: ${user['role']}",
                      style: GoogleFonts.poppins(
                        fontSize: 13,
                        color: Colors.blueGrey,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed:
                                () => _handleAction(
                                  context,
                                  ref,
                                  user['id'],
                                  'approve',
                                ),
                            icon: const Icon(
                              Icons.check_circle_outline,
                              color: Colors.white,
                              size: 20,
                            ),
                            label: Text(
                              "Approve",
                              style: GoogleFonts.poppins(),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green[600],
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed:
                                () => _handleAction(
                                  context,
                                  ref,
                                  user['id'],
                                  'reject',
                                ),
                            icon: const Icon(
                              Icons.cancel_outlined,
                              color: Colors.white,
                              size: 20,
                            ),
                            label: Text("Reject", style: GoogleFonts.poppins()),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red[600],
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
