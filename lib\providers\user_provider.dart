import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mr_garments_mobile/services/user_service.dart';

class UsersState {
  final AsyncValue<List<dynamic>> users;
  final AsyncValue<List<dynamic>> newRequests;

  const UsersState({required this.users, required this.newRequests});

  UsersState copyWith({
    AsyncValue<List<dynamic>>? users,
    AsyncValue<List<dynamic>>? newRequests,
  }) => UsersState(
    users: users ?? this.users,
    newRequests: newRequests ?? this.newRequests,
  );
}

final usersProvider = StateNotifierProvider<UsersNotifier, UsersState>(
  (ref) => UsersNotifier(),
);

class UsersNotifier extends StateNotifier<UsersState> {
  UsersNotifier()
    : super(
        const UsersState(users: AsyncLoading(), newRequests: AsyncLoading()),
      ) {
    fetchUsers();
    fetchNewUserRequests();
  }

  Future<void> fetchUsers() async {
    try {
      final users = await UserService.fetchAllUsers();
      state = state.copyWith(users: AsyncData(users.reversed.toList()));
    } catch (e, st) {
      state = state.copyWith(users: AsyncError(e, st));
    }
  }

  Future<void> addUser(Map<String, dynamic> data) async {
    await UserService.addUser(data);
    await fetchUsers();
  }

  Future<void> updateUser(int id, Map<String, dynamic> data) async {
    await UserService.updateUser(id, data);
    await fetchUsers();

   
  }

  Future<void> deactivateUser(int id) async {
    await UserService.deactivateUser(id);
    await fetchUsers();
  }

  Future<void> verifyUser(int userId, String action) async {
    await UserService.verifyUser(userId, action);
    await fetchUsers();
    await fetchNewUserRequests();
  }

  Future<void> fetchNewUserRequests() async {
    try {
      final requests = await UserService.fetchNewUserRequests();
      state = state.copyWith(
        newRequests: AsyncData(requests.reversed.toList()),
      );
    } catch (e, st) {
      state = state.copyWith(newRequests: AsyncError(e, st));
    }
  }
}
