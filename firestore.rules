rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // ==================== USERS COLLECTION ====================
    match /users/{userId} {
      // Users can read and write their own profile
      // Admin users can read all user profiles (for chat member selection)
      allow read: if request.auth != null && 
        (request.auth.uid == userId || 
         isAdmin(request.auth.uid));
      
      // Users can only update their own profile
      allow write: if request.auth != null && 
        request.auth.uid == userId;
    }
    
    // ==================== CHATS COLLECTION ====================
    match /chats/{chatId} {
      // Only chat members can read the chat document
      allow read: if request.auth != null && 
        isChatMember(chatId, request.auth.uid);
      
      // Only chat members can update chat metadata (last message, unread counts, etc.)
      allow write: if request.auth != null && 
        isChatMember(chatId, request.auth.uid);
      
      // ==================== MESSAGES SUBCOLLECTION ====================
      match /messages/{messageId} {
        // Only chat members can read messages
        allow read: if request.auth != null && 
          isChatMember(chatId, request.auth.uid);
        
        // Only chat members can send messages
        // Additional validation: sender must be the authenticated user
        allow create: if request.auth != null && 
          isChatMember(chatId, request.auth.uid) &&
          request.resource.data.senderId == request.auth.uid;
        
        // Only message sender can update their own messages (for status updates)
        allow update: if request.auth != null && 
          isChatMember(chatId, request.auth.uid) &&
          resource.data.senderId == request.auth.uid;
        
        // Only message sender can delete their own messages
        allow delete: if request.auth != null && 
          isChatMember(chatId, request.auth.uid) &&
          resource.data.senderId == request.auth.uid;
      }
    }
    
    // ==================== GROUPS COLLECTION ====================
    match /groups/{groupId} {
      // Only group members can read group data
      allow read: if request.auth != null && 
        isGroupMember(groupId, request.auth.uid);
      
      // Only group admins can update group data
      allow write: if request.auth != null && 
        isGroupAdmin(groupId, request.auth.uid);
    }
    
    // ==================== HELPER FUNCTIONS ====================
    
    // Check if user is a member of a specific chat
    function isChatMember(chatId, userId) {
      return userId in get(/databases/$(database)/documents/chats/$(chatId)).data.memberIds;
    }
    
    // Check if user is an admin (has admin role)
    function isAdmin(userId) {
      return exists(/databases/$(database)/documents/users/$(userId)) &&
        get(/databases/$(database)/documents/users/$(userId)).data.role == 'admin';
    }
    
    // Check if user is a member of a specific group
    function isGroupMember(groupId, userId) {
      let groupData = get(/databases/$(database)/documents/groups/$(groupId)).data;
      return userId in groupData.members.map(member => member.userId);
    }
    
    // Check if user is an admin of a specific group
    function isGroupAdmin(groupId, userId) {
      let groupData = get(/databases/$(database)/documents/groups/$(groupId)).data;
      let userMember = groupData.members.where(member => member.userId == userId);
      return userMember.size() > 0 && userMember[0].role == 'admin';
    }
  }
}
