rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // ==================== USERS COLLECTION ====================
    match /users/{userId} {
      // Allow authenticated users to read/write user documents
      // This is needed for chat member information
      allow read, write: if request.auth != null;
    }

    // ==================== CHATS COLLECTION ====================
    match /chats/{chatId} {
      // Only allow access if user is a member of the chat
      allow read, write: if request.auth != null &&
        isUserChatMember(chatId);

      // ==================== MESSAGES SUBCOLLECTION ====================
      match /messages/{messageId} {
        // Only chat members can read messages
        allow read: if request.auth != null &&
          isUserChatMember(chatId);

        // Only chat members can create messages
        allow create: if request.auth != null &&
          isUserChatMember(chatId);

        // Only message sender can update/delete their messages
        allow update, delete: if request.auth != null &&
          isUserChatMember(chatId) &&
          (resource.data.senderId == getUserLaravelId() ||
           request.resource.data.senderId == getUserLaravelId());
      }
    }

    // ==================== GROUPS COLLECTION ====================
    match /groups/{groupId} {
      // Allow read/write for authenticated users
      // Group membership is checked at application level
      allow read, write: if request.auth != null;
    }

    // ==================== HELPER FUNCTIONS ====================

    // Check if current user is a member of the chat
    function isUserChatMember(chatId) {
      let chatDoc = get(/databases/$(database)/documents/chats/$(chatId));
      let memberIds = chatDoc.data.memberIds;
      let currentUserId = getUserLaravelId();
      return currentUserId in memberIds;
    }

    // Get Laravel user ID from Firebase Auth UID
    // Since we're mapping Firebase UID to Laravel user ID in the users collection
    function getUserLaravelId() {
      // Try to find user document where firebaseUid matches current auth UID
      let userDoc = get(/databases/$(database)/documents/users/$(request.auth.uid));
      return userDoc != null && userDoc.data.laravelUserId != null ?
        userDoc.data.laravelUserId : request.auth.uid;
    }
  }
}
