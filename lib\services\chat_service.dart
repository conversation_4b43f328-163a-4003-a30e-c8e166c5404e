import 'dart:io';
import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:mr_garments_mobile/models/chat_user.dart';
import 'package:mr_garments_mobile/models/chat.dart';
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/models/group.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
import 'package:mr_garments_mobile/services/user_service.dart';

class ChatService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;
  static final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  // Collection references
  static final CollectionReference _usersCollection = _firestore.collection(
    'users',
  );
  static final CollectionReference _chatsCollection = _firestore.collection(
    'chats',
  );
  static final CollectionReference _groupsCollection = _firestore.collection(
    'groups',
  );

  // ==================== USER MANAGEMENT ====================

  /// Create or update user in Firestore
  static Future<void> createOrUpdateUser(ChatUser user) async {
    try {
      await _usersCollection
          .doc(user.id)
          .set(user.toMap(), SetOptions(merge: true));
    } catch (e) {
      throw Exception('Failed to create/update user: $e');
    }
  }

  /// Sync current user with Firebase from Laravel backend data
  static Future<void> syncCurrentUserWithFirebase() async {
    try {
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();
      final currentUserEmail = await SessionService.getUserEmail();
      final currentUserRole = await SessionService.getUserRole();

      if (currentUserId != null &&
          currentUserName != null &&
          currentUserEmail != null) {
        final chatUser = ChatUser(
          id: currentUserId.toString(),
          name: currentUserName,
          email: currentUserEmail,
          role: currentUserRole ?? 'user',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await createOrUpdateUser(chatUser);
      }
    } catch (e) {
      // Silently handle sync errors
    }
  }

  /// Sync all Laravel users with Firebase (for admin use)
  static Future<void> syncAllUsersWithFirebase() async {
    try {
      final apiUsers = await UserService.fetchAllUsers();

      for (final userData in apiUsers) {
        try {
          final chatUser = ChatUser(
            id: userData['id'].toString(),
            name: userData['name'] ?? 'Unknown User',
            email: userData['email'] ?? '',
            role: userData['account_type']?.toString().toLowerCase() ?? 'user',
            profileImageUrl: userData['profile_image_url'],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          await createOrUpdateUser(chatUser);
        } catch (e) {
          continue;
        }
      }
    } catch (e) {
      // Silently handle sync errors
    }
  }

  /// Get user by ID
  static Future<ChatUser?> getUser(String userId) async {
    try {
      final doc = await _usersCollection.doc(userId).get();
      if (doc.exists) {
        return ChatUser.fromDocument(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user: $e');
    }
  }

  /// Get all users with role-based filtering (for member selection)
  static Future<List<ChatUser>> getAllUsers() async {
    try {
      // Check if user is logged in first
      final isLoggedIn = await SessionService.isLoggedIn();
      if (!isLoggedIn) {
        return [];
      }

      final currentUserId = await SessionService.getUserId();
      final currentUserRole = await SessionService.getUserRole();

      if (currentUserId == null || currentUserRole == null) {
        return [];
      }

      // Fetch users from Laravel API
      final apiUsers = await UserService.fetchAllUsers();

      if (apiUsers.isEmpty) {
        return []; // Return empty list if no users found
      }

      List<ChatUser> users = [];
      for (final userData in apiUsers) {
        try {
          // Convert Laravel API user data to ChatUser
          final chatUser = ChatUser(
            id: userData['id'].toString(),
            name: userData['name'] ?? 'Unknown User',
            email: userData['email'] ?? '',
            role: userData['account_type']?.toString().toLowerCase() ?? 'user',
            profileImageUrl: userData['profile_image_url'],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );
          users.add(chatUser);
        } catch (e) {
          continue;
        }
      }

      // Apply role-based filtering
      if (currentUserRole == 'admin') {
        // Admin can see all users except themselves
        users =
            users.where((user) => user.id != currentUserId.toString()).toList();
      } else {
        // Non-admin users can only see admin users
        users = users.where((user) => user.role == 'admin').toList();
      }

      return users;
    } catch (e) {
      throw Exception('Failed to get all users: $e');
    }
  }

  /// Search users by name or email with role-based filtering
  static Future<List<ChatUser>> searchUsers(String query) async {
    try {
      // Check if user is logged in first
      final isLoggedIn = await SessionService.isLoggedIn();
      if (!isLoggedIn) {
        return [];
      }

      final currentUserId = await SessionService.getUserId();
      final currentUserRole = await SessionService.getUserRole();

      if (currentUserId == null || currentUserRole == null) {
        return [];
      }

      // Fetch users from Laravel API and filter by search query
      final apiUsers = await UserService.fetchAllUsers();

      List<ChatUser> users = [];
      for (final userData in apiUsers) {
        try {
          // Convert Laravel API user data to ChatUser
          final chatUser = ChatUser(
            id: userData['id'].toString(),
            name: userData['name'] ?? 'Unknown User',
            email: userData['email'] ?? '',
            role: userData['account_type']?.toString().toLowerCase() ?? 'user',
            profileImageUrl: userData['profile_image_url'],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          // Filter by search query (name or email)
          final name = chatUser.name.toLowerCase();
          final email = chatUser.email.toLowerCase();
          final searchQuery = query.toLowerCase();

          if (name.contains(searchQuery) || email.contains(searchQuery)) {
            users.add(chatUser);
          }
        } catch (e) {
          continue;
        }
      }

      // Apply role-based filtering
      if (currentUserRole == 'admin') {
        // Admin can search all users except themselves
        users =
            users.where((user) => user.id != currentUserId.toString()).toList();
      } else {
        // Non-admin users can only search admin users
        users = users.where((user) => user.role == 'admin').toList();
      }

      return users;
    } catch (e) {
      throw Exception('Failed to search users: $e');
    }
  }

  /// Update user online status
  static Future<void> updateUserOnlineStatus(
    String userId,
    bool isOnline,
  ) async {
    try {
      await _usersCollection.doc(userId).update({
        'isOnline': isOnline,
        'lastSeen': DateTime.now().millisecondsSinceEpoch,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      throw Exception('Failed to update online status: $e');
    }
  }

  /// Update FCM token
  static Future<void> updateFCMToken(String userId, String token) async {
    try {
      await _usersCollection.doc(userId).update({
        'fcmToken': token,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      throw Exception('Failed to update FCM token: $e');
    }
  }

  // ==================== CHAT MANAGEMENT ====================

  /// Create or get individual chat (admin can chat with anyone, others can only chat with admin)
  static Future<String> createIndividualChat(String otherUserId) async {
    try {
      final currentUserId = await SessionService.getUserId();
      final currentUserRole = await SessionService.getUserRole();

      if (currentUserId == null) throw Exception('User not logged in');

      // Generate chat ID by combining user IDs
      final chatId = _generateChatId(currentUserId.toString(), otherUserId);

      // Check if chat already exists
      final existingChat = await _chatsCollection.doc(chatId).get();
      if (existingChat.exists) {
        return chatId;
      }

      // Ensure current user is synced to Firebase
      await syncCurrentUserWithFirebase();

      // Get user details - try Firebase first, then Laravel backend
      ChatUser? currentUser = await getUser(currentUserId.toString());
      ChatUser? otherUser = await getUser(otherUserId);

      // If other user not found in Firebase, try to get from Laravel backend
      if (otherUser == null) {
        try {
          final apiUsers = await UserService.fetchAllUsers();
          final userData = apiUsers.firstWhere(
            (user) => user['id'].toString() == otherUserId,
            orElse: () => null,
          );

          if (userData != null) {
            otherUser = ChatUser(
              id: userData['id'].toString(),
              name: userData['name'] ?? 'Unknown User',
              email: userData['email'] ?? '',
              role:
                  userData['account_type']?.toString().toLowerCase() ?? 'user',
              profileImageUrl: userData['profile_image_url'],
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            );

            // Sync to Firebase
            await createOrUpdateUser(otherUser);
          }
        } catch (e) {
          // Silently handle fetch errors
        }
      }

      if (currentUser == null || otherUser == null) {
        throw Exception('User not found');
      }

      // Validate chat creation permissions
      // Admin can chat with anyone, non-admin can only chat with admin
      if (currentUserRole != 'admin' && otherUser.role != 'admin') {
        throw Exception('Only admin can initiate chats with non-admin users');
      }

      // Create new chat
      final chat = Chat(
        id: chatId,
        type: ChatType.individual,
        memberIds: [currentUserId.toString(), otherUserId],
        memberNames: {
          currentUserId.toString(): currentUser.name,
          otherUserId: otherUser.name,
        },
        memberProfileUrls: {
          currentUserId.toString(): currentUser.profileImageUrl,
          otherUserId: otherUser.profileImageUrl,
        },
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _chatsCollection.doc(chatId).set(chat.toMap());
      return chatId;
    } catch (e) {
      throw Exception('Failed to create individual chat: $e');
    }
  }

  /// Get user's chats stream with role-based filtering
  static Stream<List<Chat>> getUserChatsStream(String userId) async* {
    try {
      // Check if user is logged in first
      final isLoggedIn = await SessionService.isLoggedIn();
      if (!isLoggedIn) {
        yield [];
        return;
      }

      // Get current user's role
      final userRole = await SessionService.getUserRole();
      if (userRole == null) {
        yield [];
        return;
      }

      // Get all chats for the user
      final chatsStream = _chatsCollection
          .where('memberIds', arrayContains: userId)
          .where('isActive', isEqualTo: true)
          .orderBy('updatedAt', descending: true)
          .snapshots()
          .map(
            (snapshot) =>
                snapshot.docs.map((doc) => Chat.fromDocument(doc)).toList(),
          );

      // Cache for user roles to avoid repeated database calls
      final Map<String, String?> userRoleCache = {};

      await for (final chats in chatsStream) {
        if (userRole == 'admin') {
          // Admin can see all chats
          yield chats;
        } else {
          // Non-admin users (manufacturer, distributor, retailer) can only see chats with admin
          final filteredChats = <Chat>[];

          for (final chat in chats) {
            // Check if any of the other members in the chat is an admin
            final otherMemberIds =
                chat.memberIds.where((id) => id != userId).toList();

            bool hasAdminMember = false;
            for (final memberId in otherMemberIds) {
              // Check cache first
              String? memberRole = userRoleCache[memberId];
              if (memberRole == null) {
                final member = await getUser(memberId);
                memberRole = member?.role;
                userRoleCache[memberId] = memberRole;
              }

              if (memberRole == 'admin') {
                hasAdminMember = true;
                break;
              }
            }

            if (hasAdminMember) {
              filteredChats.add(chat);
            }
          }

          yield filteredChats;
        }
      }
    } catch (e) {
      yield [];
    }
  }

  /// Get specific chat
  static Future<Chat?> getChat(String chatId) async {
    try {
      final doc = await _chatsCollection.doc(chatId).get();
      if (doc.exists) {
        return Chat.fromDocument(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get chat: $e');
    }
  }

  // ==================== MESSAGE MANAGEMENT ====================

  /// Send text message
  static Future<void> sendTextMessage({
    required String chatId,
    required String text,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
  }) async {
    try {
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();

      if (currentUserId == null || currentUserName == null) {
        throw Exception('User not logged in');
      }

      final messageId = _generateMessageId();
      final message = Message(
        id: messageId,
        senderId: currentUserId.toString(),
        senderName: currentUserName,
        type: MessageType.text,
        text: text,
        timestamp: DateTime.now(),
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
        status: MessageStatus.sending,
      );

      // Add message to chat's messages subcollection
      await _chatsCollection
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .set(message.toMap());

      // Update message status to sent
      await _updateMessageStatus(chatId, messageId, MessageStatus.sent);

      // Update chat's last message
      await _updateChatLastMessage(
        chatId,
        message.copyWith(status: MessageStatus.sent),
      );

      // Update unreadCounts for all other members
      final chatDoc = await _chatsCollection.doc(chatId).get();
      if (chatDoc.exists) {
        final chatData = chatDoc.data() as Map<String, dynamic>;
        final memberIds = List<String>.from(chatData['memberIds'] ?? []);
        final unreadCounts = Map<String, int>.from(chatData['unreadCounts'] ?? {});
        for (final memberId in memberIds) {
          if (memberId != currentUserId.toString()) {
            unreadCounts[memberId] = (unreadCounts[memberId] ?? 0) + 1;
          }
        }
        await _chatsCollection.doc(chatId).update({'unreadCounts': unreadCounts});
      }
    } catch (e) {
      throw Exception('Failed to send text message: $e');
    }
  }

  /// Get messages stream for a chat
  static Stream<List<Message>> getMessagesStream(String chatId) {
    return _chatsCollection
        .doc(chatId)
        .collection('messages')
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => Message.fromDocument(doc)).toList(),
        );
  }

  // ==================== HELPER METHODS ====================

  /// Generate chat ID from two user IDs
  static String _generateChatId(String userId1, String userId2) {
    final sortedIds = [userId1, userId2]..sort();
    return '${sortedIds[0]}_${sortedIds[1]}';
  }

  /// Generate unique message ID
  static String _generateMessageId() {
    return DateTime.now().millisecondsSinceEpoch.toString() +
        Random().nextInt(1000).toString();
  }

  /// Update message status
  static Future<void> _updateMessageStatus(
    String chatId,
    String messageId,
    MessageStatus status,
  ) async {
    try {
      await _chatsCollection
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .update({
            'status': status.name,
            'updatedAt': DateTime.now().millisecondsSinceEpoch,
          });
    } catch (e) {
      throw Exception('Failed to update message status: $e');
    }
  }

  /// Update chat's last message
  static Future<void> _updateChatLastMessage(
    String chatId,
    Message message,
  ) async {
    try {
      await _chatsCollection.doc(chatId).update({
        'lastMessage': message.toMap(),
        'lastMessageTime': message.timestamp.millisecondsSinceEpoch,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      throw Exception('Failed to update chat last message: $e');
    }
  }

  /// Send image message
  static Future<void> sendImageMessage({
    required String chatId,
    required File imageFile,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
  }) async {
    try {
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();

      if (currentUserId == null || currentUserName == null) {
        throw Exception('User not logged in');
      }

      // Upload image to Firebase Storage
      String? imageUrl;
      try {
        imageUrl = await _uploadFile(imageFile, 'images');
      } catch (e) {
        throw Exception('Failed to upload image: $e');
      }

      final messageId = _generateMessageId();
      final message = Message(
        id: messageId,
        senderId: currentUserId.toString(),
        senderName: currentUserName,
        type: MessageType.image,
        mediaUrl: imageUrl,
        timestamp: DateTime.now(),
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
        status: MessageStatus.sending,
      );

      // Add message to chat's messages subcollection
      await _chatsCollection
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .set(message.toMap());

      // Update message status to sent
      await _updateMessageStatus(chatId, messageId, MessageStatus.sent);

      // Update chat's last message
      await _updateChatLastMessage(
        chatId,
        message.copyWith(status: MessageStatus.sent),
      );
    } catch (e) {
      throw Exception('Failed to send image message: $e');
    }
  }

  /// Send file message
  static Future<void> sendFileMessage({
    required String chatId,
    required File file,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
  }) async {
    try {
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();

      if (currentUserId == null || currentUserName == null) {
        throw Exception('User not logged in');
      }

      // Upload file to Firebase Storage
      String? fileUrl;
      try {
        fileUrl = await _uploadFile(file, 'files');
      } catch (e) {
        throw Exception('Failed to upload file: $e');
      }
      final fileName = file.path.split('/').last;
      final fileSize = await file.length();

      final messageId = _generateMessageId();
      final message = Message(
        id: messageId,
        senderId: currentUserId.toString(),
        senderName: currentUserName,
        type: MessageType.file,
        mediaUrl: fileUrl,
        fileName: fileName,
        fileSize: fileSize,
        timestamp: DateTime.now(),
        replyToMessageId: replyToMessageId,
        replyToText: replyToText,
        replyToSenderName: replyToSenderName,
        status: MessageStatus.sending,
      );

      // Add message to chat's messages subcollection
      await _chatsCollection
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .set(message.toMap());

      // Update message status to sent
      await _updateMessageStatus(chatId, messageId, MessageStatus.sent);

      // Update chat's last message
      await _updateChatLastMessage(
        chatId,
        message.copyWith(status: MessageStatus.sent),
      );
    } catch (e) {
      throw Exception('Failed to send file message: $e');
    }
  }

  /// Ensure Firebase Auth is authenticated
  static Future<void> _ensureFirebaseAuth() async {
    try {
      // Check if already authenticated
      if (_auth.currentUser != null) {
        return;
      }

      // Try to sign in anonymously for Firebase Storage access
      try {
        await _auth.signInAnonymously();
      } catch (e) {
        // If anonymous auth fails, continue without auth
      }
    } catch (e) {
      // Silently handle auth initialization errors
    }
  }

  /// Upload file to storage (Firebase Storage or Laravel backend)
  static Future<String> _uploadFile(File file, String folder) async {
    try {
      // Try Firebase Storage first
      await _ensureFirebaseAuth();

      if (_auth.currentUser != null) {
        return await _uploadToFirebaseStorage(file, folder);
      } else {
        // Fallback to Laravel backend storage
        return await _uploadToLaravelBackend(file, folder);
      }
    } catch (e) {
      // If Firebase fails, try Laravel backend
      try {
        return await _uploadToLaravelBackend(file, folder);
      } catch (backendError) {
        throw Exception(
          'Failed to upload file: Firebase: $e, Backend: $backendError',
        );
      }
    }
  }

  /// Upload file to Firebase Storage
  static Future<String> _uploadToFirebaseStorage(
    File file,
    String folder,
  ) async {
    try {
      // Ensure user is authenticated
      if (_auth.currentUser == null) {
        await _auth.signInAnonymously();
      }

      final fileName =
          '${DateTime.now().millisecondsSinceEpoch}_${file.path.split('/').last}';
      final ref = _storage.ref().child('$folder/$fileName');

      // Add metadata for better file handling
      final metadata = SettableMetadata(
        contentType: _getContentType(file.path),
        customMetadata: {
          'uploadedBy': _auth.currentUser?.uid ?? 'anonymous',
          'uploadedAt': DateTime.now().toIso8601String(),
        },
      );

      final uploadTask = ref.putFile(file, metadata);
      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();

      return downloadUrl;
    } catch (e) {
      throw Exception('Failed to upload file to Firebase Storage: $e');
    }
  }

  /// Get content type based on file extension
  static String _getContentType(String filePath) {
    final extension = filePath.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      case 'pdf':
        return 'application/pdf';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      default:
        return 'application/octet-stream';
    }
  }

  /// Upload file to Laravel backend
  static Future<String> _uploadToLaravelBackend(
    File file,
    String folder,
  ) async {
    try {
      // For now, we'll still try Firebase Storage even in fallback
      // since Laravel backend file upload is not implemented
      final fileName =
          '${DateTime.now().millisecondsSinceEpoch}_${file.path.split('/').last}';
      final ref = _storage.ref().child('$folder/$fileName');

      final uploadTask = ref.putFile(file);
      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();

      return downloadUrl;
    } catch (e) {
      // If Firebase also fails, return a placeholder
      final fileName =
          '${DateTime.now().millisecondsSinceEpoch}_${file.path.split('/').last}';
      return 'https://mrgarment.braincavesoft.com/storage/$folder/$fileName';
    }
  }

  /// Mark message as read
  static Future<void> markMessageAsRead(
    String chatId,
    String messageId,
    String userId,
  ) async {
    try {
      await _chatsCollection
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .update({
            'readBy': FieldValue.arrayUnion([userId]),
            'status': MessageStatus.read.name,
          });
    } catch (e) {
      throw Exception('Failed to mark message as read: $e');
    }
  }

  /// Mark all messages as read for a user
  static Future<void> markAllMessagesAsRead(
    String chatId,
    String userId,
  ) async {
    try {
      final messagesQuery =
          await _chatsCollection
              .doc(chatId)
              .collection('messages')
              .where('senderId', isNotEqualTo: userId)
              .get();

      final batch = _firestore.batch();
      for (final doc in messagesQuery.docs) {
        final messageData = doc.data();
        final readBy = List<String>.from(messageData['readBy'] ?? []);

        if (!readBy.contains(userId)) {
          batch.update(doc.reference, {
            'readBy': FieldValue.arrayUnion([userId]),
            'status': MessageStatus.read.name,
          });
        }
      }

      await batch.commit();
      // Set unread count to 0 for this user in the chat document
      await _chatsCollection.doc(chatId).update({
        'unreadCounts.$userId': 0,
      });
    } catch (e) {
      throw Exception('Failed to mark all messages as read: $e');
    }
  }

  /// Delete message
  static Future<void> deleteMessage(String chatId, String messageId) async {
    try {
      // Get the chat document to check if this is the last message
      final chatDoc = await _chatsCollection.doc(chatId).get();
      final chatData = chatDoc.data() as Map<String, dynamic>?;

      // Check if the message being deleted is the last message
      final lastMessageData = chatData?['lastMessage'] as Map<String, dynamic>?;
      final isLastMessage = lastMessageData?['id'] == messageId;

      // Delete the message
      await _chatsCollection
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .delete();

      // If this was the last message, update the chat's last message
      if (isLastMessage) {
        await _updateChatLastMessageAfterDeletion(chatId);
      }
    } catch (e) {
      throw Exception('Failed to delete message: $e');
    }
  }

  /// Update chat's last message after a message deletion
  static Future<void> _updateChatLastMessageAfterDeletion(String chatId) async {
    try {
      // Get the most recent message (excluding the deleted one)
      final messagesQuery =
          await _chatsCollection
              .doc(chatId)
              .collection('messages')
              .orderBy('timestamp', descending: true)
              .limit(1)
              .get();

      if (messagesQuery.docs.isNotEmpty) {
        // Update with the new last message
        final lastMessage = Message.fromDocument(messagesQuery.docs.first);
        await _updateChatLastMessage(chatId, lastMessage);
      } else {
        // No messages left, clear the last message
        await _chatsCollection.doc(chatId).update({
          'lastMessage': null,
          'lastMessageTime': null,
          'updatedAt': DateTime.now().millisecondsSinceEpoch,
        });
      }
    } catch (e) {
      throw Exception('Failed to update last message after deletion: $e');
    }
  }

  /// Forward message
  static Future<void> forwardMessage({
    required String fromChatId,
    required String messageId,
    required List<String> toChatIds,
  }) async {
    try {
      // Get original message
      final messageDoc =
          await _chatsCollection
              .doc(fromChatId)
              .collection('messages')
              .doc(messageId)
              .get();

      if (!messageDoc.exists) throw Exception('Message not found');

      final originalMessage = Message.fromDocument(messageDoc);
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();

      if (currentUserId == null || currentUserName == null) {
        throw Exception('User not logged in');
      }

      // Forward to each chat
      for (final chatId in toChatIds) {
        final newMessageId = _generateMessageId();
        final forwardedMessage = originalMessage.copyWith(
          id: newMessageId,
          senderId: currentUserId.toString(),
          senderName: currentUserName,
          timestamp: DateTime.now(),
          isForwarded: true,
          replyToMessageId: null, // Remove reply context when forwarding
        );

        await _chatsCollection
            .doc(chatId)
            .collection('messages')
            .doc(newMessageId)
            .set(forwardedMessage.toMap());

        await _updateChatLastMessage(chatId, forwardedMessage);
      }
    } catch (e) {
      throw Exception('Failed to forward message: $e');
    }
  }

  // ==================== GROUP MANAGEMENT ====================

  /// Create group
  static Future<String> createGroup({
    required String groupName,
    String? description,
    required List<String> memberIds,
    File? groupImage,
  }) async {
    try {
      final currentUserId = await SessionService.getUserId();
      final currentUserName = await SessionService.getUserName();

      if (currentUserId == null || currentUserName == null) {
        throw Exception('User not logged in');
      }

      final groupId = _generateGroupId();
      String? groupImageUrl;

      // Upload group image if provided
      if (groupImage != null) {
        try {
          groupImageUrl = await _uploadFile(groupImage, 'group_images');
        } catch (e) {
          // If image upload fails, continue without image
          groupImageUrl = null;
        }
      }

      // Get member details
      final members = <GroupMember>[];
      final memberNames = <String, String>{};
      final memberProfileUrls = <String, String?>{};

      // Ensure current user is synced to Firebase
      await syncCurrentUserWithFirebase();

      // Add creator as admin
      ChatUser? creator = await getUser(currentUserId.toString());
      if (creator == null) {
        // If creator not found in Firebase, create from session data
        creator = ChatUser(
          id: currentUserId.toString(),
          name: currentUserName,
          email: await SessionService.getUserEmail() ?? '',
          role: await SessionService.getUserRole() ?? 'user',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        await createOrUpdateUser(creator);
      }

      members.add(
        GroupMember(
          userId: currentUserId.toString(),
          name: creator.name,
          profileUrl: creator.profileImageUrl,
          role: 'admin',
          joinedAt: DateTime.now(),
        ),
      );
      memberNames[currentUserId.toString()] = creator.name;
      memberProfileUrls[currentUserId.toString()] = creator.profileImageUrl;

      // Add other members
      for (final memberId in memberIds) {
        if (memberId != currentUserId.toString()) {
          ChatUser? user = await getUser(memberId);

          // If user not found in Firebase, try to get from Laravel backend
          if (user == null) {
            try {
              final apiUsers = await UserService.fetchAllUsers();
              final userData = apiUsers.firstWhere(
                (u) => u['id'].toString() == memberId,
                orElse: () => null,
              );

              if (userData != null) {
                user = ChatUser(
                  id: userData['id'].toString(),
                  name: userData['name'] ?? 'Unknown User',
                  email: userData['email'] ?? '',
                  role:
                      userData['account_type']?.toString().toLowerCase() ??
                      'user',
                  profileImageUrl: userData['profile_image_url'],
                  createdAt: DateTime.now(),
                  updatedAt: DateTime.now(),
                );

                // Sync to Firebase
                await createOrUpdateUser(user);
              }
            } catch (e) {
              // Silently handle member fetch errors
            }
          }

          if (user != null) {
            members.add(
              GroupMember(
                userId: memberId,
                name: user.name,
                profileUrl: user.profileImageUrl,
                joinedAt: DateTime.now(),
              ),
            );
            memberNames[memberId] = user.name;
            memberProfileUrls[memberId] = user.profileImageUrl;
          }
        }
      }

      // Create group
      final group = Group(
        id: groupId,
        name: groupName,
        description: description,
        imageUrl: groupImageUrl,
        createdBy: currentUserId.toString(),
        members: members,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _groupsCollection.doc(groupId).set(group.toMap());

      // Create corresponding chat
      final chat = Chat(
        id: groupId,
        type: ChatType.group,
        memberIds: members.map((m) => m.userId).toList(),
        memberNames: memberNames,
        memberProfileUrls: memberProfileUrls,
        groupName: groupName,
        groupImageUrl: groupImageUrl,
        groupDescription: description,
        createdBy: currentUserId.toString(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _chatsCollection.doc(groupId).set(chat.toMap());

      return groupId;
    } catch (e) {
      throw Exception('Failed to create group: $e');
    }
  }

  /// Get user's groups
  static Future<List<Group>> getUserGroups(String userId) async {
    try {
      final querySnapshot =
          await _groupsCollection
              .where(
                'members',
                arrayContains: {'userId': userId, 'isActive': true},
              )
              .where('isActive', isEqualTo: true)
              .get();

      return querySnapshot.docs.map((doc) => Group.fromDocument(doc)).toList();
    } catch (e) {
      throw Exception('Failed to get user groups: $e');
    }
  }

  /// Get group by ID
  static Future<Group?> getGroup(String groupId) async {
    try {
      final doc = await _groupsCollection.doc(groupId).get();
      if (doc.exists) {
        return Group.fromDocument(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get group: $e');
    }
  }

  /// Add member to group
  static Future<void> addMemberToGroup(String groupId, String userId) async {
    try {
      final user = await getUser(userId);
      if (user == null) throw Exception('User not found');

      final newMember = GroupMember(
        userId: userId,
        name: user.name,
        profileUrl: user.profileImageUrl,
        joinedAt: DateTime.now(),
      );

      await _groupsCollection.doc(groupId).update({
        'members': FieldValue.arrayUnion([newMember.toMap()]),
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });

      // Update corresponding chat
      await _chatsCollection.doc(groupId).update({
        'memberIds': FieldValue.arrayUnion([userId]),
        'memberNames.$userId': user.name,
        'memberProfileUrls.$userId': user.profileImageUrl,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      throw Exception('Failed to add member to group: $e');
    }
  }

  /// Remove member from group
  static Future<void> removeMemberFromGroup(
    String groupId,
    String userId,
  ) async {
    try {
      // Get group to find the member
      final group = await getGroup(groupId);
      if (group == null) throw Exception('Group not found');

      final member = group.getMember(userId);
      if (member == null) throw Exception('Member not found');

      await _groupsCollection.doc(groupId).update({
        'members': FieldValue.arrayRemove([member.toMap()]),
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });

      // Update corresponding chat
      await _chatsCollection.doc(groupId).update({
        'memberIds': FieldValue.arrayRemove([userId]),
        'memberNames.$userId': FieldValue.delete(),
        'memberProfileUrls.$userId': FieldValue.delete(),
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      throw Exception('Failed to remove member from group: $e');
    }
  }

  // ==================== FCM MANAGEMENT ====================

  /// Initialize FCM
  static Future<void> initializeFCM() async {
    try {
      // Request permission
      final settings = await _messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        // Get FCM token
        final token = await _messaging.getToken();
        if (token != null) {
          final currentUserId = await SessionService.getUserId();
          if (currentUserId != null) {
            await updateFCMToken(currentUserId.toString(), token);
          }
        }

        // Listen for token refresh
        _messaging.onTokenRefresh.listen((newToken) async {
          final currentUserId = await SessionService.getUserId();
          if (currentUserId != null) {
            await updateFCMToken(currentUserId.toString(), newToken);
          }
        });
      }
    } catch (e) {
      throw Exception('Failed to initialize FCM: $e');
    }
  }

  // ==================== HELPER METHODS ====================

  /// Generate unique group ID
  static String _generateGroupId() {
    return 'group_${DateTime.now().millisecondsSinceEpoch}_${Random().nextInt(1000)}';
  }

  /// Clear chat messages (keep chat but remove all messages)
  static Future<void> clearChat(String chatId) async {
    try {
      // Delete all messages in the chat
      final messagesSnapshot =
          await _chatsCollection.doc(chatId).collection('messages').get();

      final batch = _firestore.batch();
      for (final doc in messagesSnapshot.docs) {
        batch.delete(doc.reference);
      }
      await batch.commit();

      // Clear the last message in the chat
      await _chatsCollection.doc(chatId).update({
        'lastMessage': null,
        'lastMessageTime': null,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      throw Exception('Failed to clear chat: $e');
    }
  }

  /// Delete chat
  static Future<void> deleteChat(String chatId) async {
    try {
      // Delete all messages in the chat
      final messagesSnapshot =
          await _chatsCollection.doc(chatId).collection('messages').get();

      final batch = _firestore.batch();
      for (final doc in messagesSnapshot.docs) {
        batch.delete(doc.reference);
      }
      await batch.commit();

      // Delete the chat
      await _chatsCollection.doc(chatId).delete();

      // If it's a group, also delete the group
      final chat = await getChat(chatId);
      if (chat?.isGroup == true) {
        await _groupsCollection.doc(chatId).delete();
      }
    } catch (e) {
      throw Exception('Failed to delete chat: $e');
    }
  }

  /// Get unread message count for user
  static Future<int> getUnreadMessageCount(String userId) async {
    try {
      int totalUnread = 0;

      final chatsSnapshot =
          await _chatsCollection
              .where('memberIds', arrayContains: userId)
              .where('isActive', isEqualTo: true)
              .get();

      for (final chatDoc in chatsSnapshot.docs) {
        final chat = Chat.fromDocument(chatDoc);
        totalUnread += chat.getUnreadCount(userId);
      }

      return totalUnread;
    } catch (e) {
      throw Exception('Failed to get unread message count: $e');
    }
  }
}
